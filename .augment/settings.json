{"augment.advanced": {"mcpServers": [{"name": "context7", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, {"name": "sequential-thinking", "command": "uvx", "args": ["--from", "git+https://github.com/arben-adm/mcp-sequential-thinking", "--with", "<PERSON><PERSON><PERSON>", "mcp-sequential-thinking"]}, {"name": "mcp-feedback-enhanced", "command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "env": {"FORCE_WEB": "true", "MCP_DEBUG": "false"}}]}}