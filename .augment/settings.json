{"augment.advanced": {"mcpServers": [{"name": "context7", "command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, {"name": "sequential-thinking", "command": "npx", "args": ["-y", "@zengwenliang/mcp-server-sequential-thinking"]}, {"name": "mcp-feedback-enhanced", "command": "uvx", "args": ["mcp-feedback-enhanced"], "env": {"MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false"}}]}}