# MCP 工具配置指南 - 更新版

## 概述

根据官方文档，我已经为您重新配置了两个主要的 MCP 工具：
- **Context7**: 来自 Upstash 的官方 MCP 服务器，提供最新代码文档
- **Sequential Thinking**: 来自 zengwenliang416 的 MCP 服务器，提供结构化思维过程
- **Feedback Enhanced**: 交互式反馈的 MCP 服务器（保持不变）

## 最新配置

配置文件位置：`.augment/settings.json`

### 完整 JSON 配置

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "context7",
        "command": "npx",
        "args": ["-y", "@upstash/context7-mcp"]
      },
      {
        "name": "sequential-thinking",
        "command": "npx",
        "args": ["-y", "@zengwenliang/mcp-server-sequential-thinking"]
      },
      {
        "name": "mcp-feedback-enhanced",
        "command": "uvx",
        "args": ["mcp-feedback-enhanced"],
        "env": {
          "MCP_WEB_HOST": "127.0.0.1",
          "MCP_WEB_PORT": "8765",
          "MCP_DEBUG": "false"
        }
      }
    ]
  }
}
```

## 配置变更说明

### 1. Context7 (保持不变)
- **来源**: https://github.com/upstash/context7
- **包名**: `@upstash/context7-mcp`
- **命令**: `npx -y @upstash/context7-mcp`
- **功能**: 提供最新的代码文档和 API 参考

### 2. Sequential Thinking (重要更新)
- **来源**: https://github.com/zengwenliang416/mcp-server-sequential-thinking
- **包名**: `@zengwenliang/mcp-server-sequential-thinking`
- **命令**: `npx -y @zengwenliang/mcp-server-sequential-thinking`
- **变更原因**: 
  - 使用官方发布的 npm 包，更稳定可靠
  - 避免了从 GitHub 直接安装的复杂性
  - 更好的版本管理和依赖处理

### 3. Feedback Enhanced (保持不变)
- **包名**: `mcp-feedback-enhanced`
- **命令**: `uvx mcp-feedback-enhanced`
- **功能**: 提供交互式用户反馈界面

## 工具功能详解

### Context7 工具
- `resolve-library-id`: 解析库名称为 Context7 兼容的库 ID
- `get-library-docs`: 获取指定库的文档

**使用示例**:
```
实现基本的 Supabase 认证功能。use context7
```

### Sequential Thinking 工具
- `sequential_thinking`: 促进详细的逐步思考过程

**参数说明**:
- `thought`: 当前思考步骤
- `nextThoughtNeeded`: 是否需要下一个思考步骤
- `thoughtNumber`: 当前思考编号
- `totalThoughts`: 预估总思考数量
- `isRevision`: 是否为修订
- `revisesThought`: 修订的思考编号
- `branchFromThought`: 分支起点思考编号
- `branchId`: 分支标识符

### Feedback Enhanced 工具
- `interactive_feedback`: 收集用户反馈
- 支持 Web UI 和桌面应用双界面
- 提供实时反馈机制

## 环境要求

### 必需依赖
- **Node.js**: Context7 和 Sequential Thinking 需要
- **NPX**: 用于运行 npm 包
- **UV Package Manager**: Feedback Enhanced 需要

### 验证安装
```bash
# 验证 Node.js 和 NPX
node --version
npx --version

# 验证 UV
uv --version
```

## 测试配置

### 测试 Context7
```bash
npx -y @upstash/context7-mcp
```

### 测试 Sequential Thinking
```bash
npx -y @zengwenliang/mcp-server-sequential-thinking
```

### 测试 Feedback Enhanced
```bash
uvx mcp-feedback-enhanced
```

## 使用建议

### 1. Context7 最佳实践
- 在提示中添加 `use context7` 来获取最新文档
- 使用具体的库 ID 格式：`/库名/子模块`
- 示例：`use library /supabase/supabase for api and docs`

### 2. Sequential Thinking 最佳实践
- 用于复杂问题的系统性分析
- 支持思维分支和修订
- 适合需要多步骤推理的任务

### 3. Feedback Enhanced 最佳实践
- 在任何需要用户确认的流程中使用
- 支持图像上传和多语言界面
- 提供会话管理和历史记录

## 故障排除

### 常见问题

1. **MCP 服务器无法启动**
   - 重启 Augment Code / VS Code / Cursor
   - 检查网络连接
   - 验证 Node.js 版本 (需要 v18+)

2. **NPX 包安装失败**
   - 清除 npm 缓存：`npm cache clean --force`
   - 使用不同的包管理器：`yarn` 或 `pnpm`

3. **Sequential Thinking 连接错误**
   - 确保使用正确的包名：`@zengwenliang/mcp-server-sequential-thinking`
   - 检查 npm 注册表连接

4. **Feedback Enhanced 端口冲突**
   - 修改端口：将 `MCP_WEB_PORT` 改为其他值（如 8766）
   - 检查防火墙设置

## 更新日志

- **2025-01-XX**: 更新 Sequential Thinking 为官方 npm 包
- **2025-01-XX**: 简化配置，移除复杂的 GitHub 安装方式
- **2025-01-XX**: 优化环境变量配置

## 支持资源

- **Context7 官方文档**: https://github.com/upstash/context7
- **Sequential Thinking 文档**: https://github.com/zengwenliang416/mcp-server-sequential-thinking
- **Feedback Enhanced 文档**: https://github.com/Minidoracat/mcp-feedback-enhanced

如有问题，请参考各工具的官方文档或在相应的 GitHub 仓库提交 Issue。
