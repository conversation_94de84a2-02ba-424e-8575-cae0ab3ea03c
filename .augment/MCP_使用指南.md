# Augment MCP 服务器配置使用指南

## 概述

已成功为 Augment 配置了三个 MCP (Model Context Protocol) 服务器：
- **Context7**: 提供最新的代码文档和API信息
- **Sequential Thinking**: 提供结构化思维和问题分析能力
- **Feedback Enhanced**: 提供交互式反馈和确认机制

## 配置文件位置

配置文件位于：`.augment/settings.json`

## 已配置的 MCP 服务器

### 1. Context7 MCP 服务器
- **功能**: 提供最新的代码文档、API参考和库信息
- **命令**: `npx -y @upstash/context7-mcp@latest`
- **用途**: 
  - 获取最新的库文档
  - 解析库ID和版本信息
  - 提供代码示例和最佳实践

### 2. Sequential Thinking MCP 服务器
- **功能**: 提供结构化思维和逐步问题分析
- **命令**: `uvx --from git+https://github.com/arben-adm/mcp-sequential-thinking --with portalocker mcp-sequential-thinking`
- **用途**:
  - 处理复杂思维过程
  - 生成思维总结
  - 清理思维历史记录

### 3. Feedback Enhanced MCP 服务器
- **功能**: 提供交互式反馈和人机交互确认
- **命令**: `uvx mcp-feedback-enhanced@latest`
- **环境变量**:
  - `FORCE_WEB=true`: 强制使用Web界面
  - `MCP_DEBUG=false`: 关闭调试模式
- **用途**:
  - 交互式反馈收集
  - 用户确认机制
  - Web界面交互

## 系统要求

### 已安装的依赖
- **Node.js**: 用于运行 Context7 (npx 命令)
- **Python 3.13.5**: 系统Python版本
- **UV 0.8.4**: Python包管理器，用于运行 Sequential Thinking 和 Feedback Enhanced

### 验证安装
所有服务器已通过测试验证：
- ✅ Context7: 可正常显示帮助信息
- ✅ Sequential Thinking: 可正常启动服务器
- ✅ Feedback Enhanced: 可正常启动Web服务器

## 使用方法

### 在 Augment 中启用
1. 重启 VS Code 编辑器
2. MCP 服务器将自动加载
3. 在 Augment Agent 对话中，这些工具将自动可用

### 工具调用示例
- **Context7**: 询问最新的库文档或API信息
- **Sequential Thinking**: 请求结构化分析复杂问题
- **Feedback Enhanced**: 需要用户确认的交互式操作

## 配置文件内容

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "context7",
        "command": "npx",
        "args": ["-y", "@upstash/context7-mcp@latest"]
      },
      {
        "name": "sequential-thinking",
        "command": "uvx",
        "args": [
          "--from",
          "git+https://github.com/arben-adm/mcp-sequential-thinking",
          "--with",
          "portalocker",
          "mcp-sequential-thinking"
        ]
      },
      {
        "name": "mcp-feedback-enhanced",
        "command": "uvx",
        "args": ["mcp-feedback-enhanced@latest"],
        "env": {
          "FORCE_WEB": "true",
          "MCP_DEBUG": "false"
        }
      }
    ]
  }
}
```

## 故障排除

### 常见问题
1. **MCP 服务器无法启动**: 检查依赖是否正确安装
2. **权限问题**: 确保有执行 npx 和 uvx 命令的权限
3. **网络问题**: Context7 需要网络连接获取最新文档

### 日志查看
- Augment 会在输出面板显示 MCP 服务器的启动日志
- 如有错误，检查 VS Code 的开发者控制台

## 更新和维护

### 更新服务器
- **Context7**: 自动使用最新版本 (`@latest`)
- **Sequential Thinking**: 从 Git 仓库获取最新代码
- **Feedback Enhanced**: 自动使用最新版本 (`@latest`)

### 配置修改
如需修改配置，编辑 `.augment/settings.json` 文件并重启 VS Code。

## 支持和文档

- **Context7**: https://github.com/upstash/context7
- **Sequential Thinking**: https://github.com/arben-adm/mcp-sequential-thinking
- **Feedback Enhanced**: PyPI 包文档
- **Augment MCP**: https://docs.augmentcode.com/setup-augment/mcp
